import React from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";

interface MainLayoutProps {
  children: React.ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  return (
    <div className="min-h-screen text-white">
      <header className="bg-linear-to-b from-background to-90% py-6 px-4 md:px-8 fixed z-2 h-[160px] w-screen">
        <div className="mx-auto flex justify-between items-center">
          <Link href="/">
            <Image
              src={`/grand production logo.png`}
              alt="grand production logo"
              width="120"
              height="60"
            />
          </Link>
          <nav>
            <ul className="flex space-x-6 text-primary uppercase font-semibold">
              <li>
                <Link href="/work">
                  <span>Work</span>
                </Link>
              </li>
              <li>
                <Link href="/about">
                  <span>About</span>
                </Link>
              </li>
              <li>
                <Link href="/contact">
                  <span>Contact</span>
                </Link>
              </li>
            </ul>
          </nav>
        </div>
      </header>
      <main>{children}</main>
      <footer className="py-8 px-4 md:px-8 bg-linear-to-t from-background via-background via-10% uppercase">
        <div className="flex justify-between text-primary">
          <p className="">Grand Production</p>
          <p>
            <a
              href="https://www.youtube.com/@MohamedOunejjar"
              target="_blank"
              rel="noopener noreferrer"
              className="underline font-semibold"
            >
              Youtube
            </a>
            /
            <a
              href="https://www.instagram.com/ounejjar__mohamed/"
              target="_blank"
              rel="noopener noreferrer"
              className="underline font-semibold"
            >
              Instagram
            </a>
          </p>
        </div>
        <div className="w-full h-[1px] bg-secondary my-2" />
        <div className="flex justify-between text-secondary text-sm">
          <p className="">© {new Date().getFullYear()} [Copyright]</p>
          <p>
            Website by{" "}
            <a
              href="https://www.linkedin.com/in/adouz/"
              target="_blank"
              rel="noopener noreferrer"
              className="underline font-semibold"
            >
              Abdellatif Douz
            </a>
          </p>
        </div>
      </footer>
    </div>
  );
}
