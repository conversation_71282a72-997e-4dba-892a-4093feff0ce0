@import "tailwindcss";
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #121212;
  --foreground: #171717;
  --primary: #FAD400;
  --secondary: #CFA50F;
  --accent: #ECF1F4;

}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --font-sans: var(--font-poppins);
  --font-mono: var(--font-poppins);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-poppins);
}


@layer components {
  .btn-primary {
    @apply bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-md font-medium transition-colors;
  }

  .section-title {
    @apply text-3xl font-bold mb-8 relative;
  }

  .section-title::after {
    @apply content-[''] absolute left-0 bottom-0 w-24 h-1 bg-red-600;
  }
}

/* Additional custom styles */
