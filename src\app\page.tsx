"use client";

import { motion, useScroll, useTransform } from "framer-motion";
import { useRef } from "react";
import MaskSplitText from "@/components/ui/MaskSplitText";

export default function Home() {
  const scrollContainerRef = useRef<HTMLElement | null>(null);

  const { scrollYProgress } = useScroll(); // Get scroll progress (0 to 1)
  const blurValue = useTransform(
    scrollYProgress,
    [0, 0.5],
    ["blur(0px)", "blur(20px)"]
  ); // Map progress to blur

  return (
    <div>
      <section ref={scrollContainerRef}>
        <div className="relative h-screen overflow-hidden">
          <motion.div
            style={{ filter: blurValue }}
            className="
          fixed
          top-1/2 
          left-1/2 
          -translate-x-1/2 
          -translate-y-1/2
          w-screen
          h-[56.25vw]
          min-h-[102vh] 
          min-w-[178vh]
          border-none 
          pointer-events-none
          opacity-50
          -z-10
        "
          >
            <video
              className="w-full h-full"
              muted
              loop
              playsInline
              preload="none"
              autoPlay
              src="/previews/Grandproduction background.mp4"
            />
          </motion.div>
        </div>
      </section>

      <section className="py-16 px-4 md:px-8">
        <div className="">
          <div className="text-7xl uppercase text-primary font-bold text-center my-5">
            <MaskSplitText text="Work" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(40)].map((item) => (
              <motion.div
                key={item}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                // viewport={{ once: true }}
                transition={{ duration: 0.5, delay: item * 0.1 }}
                className="rounded-lg overflow-hidden"
              >
                <motion.div
                  initial="hidden"
                  whileHover="hovered"
                  className="relative aspect-video cursor-pointer"
                >
                  <motion.div
                    variants={{
                      hidden: { opacity: 0 },
                      hovered: { opacity: 1 },
                    }}
                    transition={{ duration: 0.3 }}
                    className="absolute w-full h-full inset-0 bg-background/80 flex items-center justify-center text-center"
                  >
                    <div className="p-4">
                      <h3 className="text-2xl font-semibold">Project Title</h3>
                      <p className="text-sm text-gray-400 ">2050</p>
                    </div>
                  </motion.div>
                  <video
                    src="/previews/test video.mp4"
                    muted
                    loop
                    playsInline
                    preload="none"
                    autoPlay
                  />
                </motion.div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
